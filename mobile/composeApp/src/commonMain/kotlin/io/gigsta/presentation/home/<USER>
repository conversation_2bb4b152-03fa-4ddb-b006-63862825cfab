package io.gigsta.presentation.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Article
import androidx.compose.material.icons.automirrored.filled.Feed
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.domain.model.*
import io.gigsta.domain.model.MenuItem
import io.gigsta.domain.utils.getResumeTemplateById
import io.gigsta.domain.utils.DateUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: HomeViewModel = viewModel(),
    onSignOut: () -> Unit = {}
) {
    val uiState = viewModel.uiState
    val currentMenuItem = viewModel.menuItems[uiState.selectedTabIndex]

    Scaffold(
        topBar = {
            HomeTopBar(
                title = currentMenuItem.title,
                onMenuClick = { /* Handle menu */ },
                onSearchClick = { /* Handle search */ },
                onSignOut = onSignOut
            )
        },
        bottomBar = {
            HomeBottomNavigationBar(
                selectedTabIndex = uiState.selectedTabIndex,
                onTabSelected = viewModel::onTabSelected,
                menuItems = viewModel.menuItems
            )
        }
    ) { paddingValues ->
        HomeContent(
            modifier = Modifier.padding(paddingValues),
            menuItem = currentMenuItem,
            historyItems = uiState.historyItems,
            isLoading = uiState.isLoading,
            error = uiState.error,
            onCreateNewItem = viewModel::onCreateNewItem,
            onHistoryItemClick = viewModel::onHistoryItemClick
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HomeTopBar(
    title: String,
    onMenuClick: () -> Unit,
    onSearchClick: () -> Unit,
    onSignOut: () -> Unit
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            )
        },
        navigationIcon = {
            IconButton(onClick = onMenuClick) {
                Icon(Icons.Default.Menu, contentDescription = "Menu")
            }
        },
        actions = {
            IconButton(onClick = onSearchClick) {
                Icon(Icons.Default.Search, contentDescription = "Search")
            }
            IconButton(onClick = onSignOut) {
                Icon(Icons.Default.ExitToApp, contentDescription = "Sign Out")
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    )
}

@Composable
private fun HomeContent(
    modifier: Modifier = Modifier,
    menuItem: MenuItem,
    historyItems: List<BaseHistoryItem>,
    isLoading: Boolean,
    error: String?,
    onCreateNewItem: () -> Unit,
    onHistoryItemClick: (BaseHistoryItem) -> Unit
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            CreateNewItemCard(
                menuTitle = menuItem.title,
                onClick = onCreateNewItem
            )
        }

        when {
            isLoading -> {
                item {
                    LoadingIndicator()
                }
            }
            error != null -> {
                item {
                    ErrorMessage(message = error)
                }
            }
            historyItems.isEmpty() -> {
                item {
                    EmptyState(menuTitle = menuItem.title)
                }
            }
            else -> {
                items(historyItems) { historyItem ->
                    HistoryItemCard(
                        historyItem = historyItem,
                        onClick = { onHistoryItemClick(historyItem) }
                    )
                }
            }
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CreateNewItemCard(
    menuTitle: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primary
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "Add new",
                tint = MaterialTheme.colorScheme.onPrimary
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Tambah $menuTitle",
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun LoadingIndicator() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ErrorMessage(message: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Text(
            text = message,
            modifier = Modifier.padding(16.dp),
            color = MaterialTheme.colorScheme.onErrorContainer
        )
    }
}

@Composable
private fun EmptyState(menuTitle: String) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Info,
            contentDescription = null,
            modifier = Modifier.size(48.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "Belum ada $menuTitle",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = "Buat $menuTitle untuk memulai",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HistoryItemCard(
    historyItem: BaseHistoryItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            ItemIcon(historyItem)

            Spacer(modifier = Modifier.width(12.dp))

            ItemContent(
                historyItem = historyItem,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun ItemIcon(historyItem: BaseHistoryItem) {
    val (icon, backgroundColor, iconColor) = when (historyItem.type) {
        HistoryItemType.CV_BUILDER -> Triple(
            Icons.AutoMirrored.Default.Feed,
            MaterialTheme.colorScheme.primaryContainer,
            MaterialTheme.colorScheme.onPrimaryContainer
        )
        HistoryItemType.APPLICATION_LETTER -> Triple(
            Icons.AutoMirrored.Default.Article,
            MaterialTheme.colorScheme.secondaryContainer,
            MaterialTheme.colorScheme.onSecondaryContainer
        )
        HistoryItemType.EMAIL_APPLICATION -> Triple(
            Icons.Default.Email,
            MaterialTheme.colorScheme.tertiaryContainer,
            MaterialTheme.colorScheme.onTertiaryContainer
        )
    }

    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = iconColor,
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun ItemContent(
    historyItem: BaseHistoryItem,
    modifier: Modifier = Modifier
) {
    when (historyItem) {
        is ResumeHistoryItem -> ResumeItemContent(historyItem, modifier)
        is LetterHistoryItem -> LetterItemContent(historyItem, modifier)
        is EmailHistoryItem -> EmailItemContent(historyItem, modifier)
    }
}

@Composable
private fun ResumeItemContent(
    historyItem: ResumeHistoryItem,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // Template name
        historyItem.templateName?.let { name ->
            Text(
                text = name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // Target position
        historyItem.structuredData?.targetPosition?.let { position ->
            if (position.isNotBlank()) {
                Text(
                    text = position,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }

        // Professional summary
        historyItem.structuredData?.professionalSummary?.let { summary ->
            if (summary.isNotBlank()) {
                Text(
                    text = summary,
                    fontSize = 13.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }

        // Created date in device locale format
        Text(
            text = "Dibuat pada ${DateUtils.formatDateForIndonesianLocale(historyItem.createdAt)}",
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun LetterItemContent(
    historyItem: LetterHistoryItem,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        historyItem.templateName?.let { name ->
            Text(
                text = name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        // Show preview of letter content
        historyItem.plainText?.let { text ->
            Text(
                text = text,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Dibuat pada ${historyItem.createdAt}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun EmailItemContent(
    historyItem: EmailHistoryItem,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = historyItem.subject,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        // Show email body preview
        Text(
            text = historyItem.body.take(120).replace("\n", " ").trim(),
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )

        Text(
            text = "Dibuat pada ${historyItem.createdAt}",
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun HomeBottomNavigationBar(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    menuItems: List<MenuItem>
) {
    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp
    ) {
        menuItems.forEachIndexed { index, menuItem ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = menuItem.icon,
                        contentDescription = menuItem.title
                    )
                },
                label = {
                    Text(
                        text = menuItem.title,
                        fontSize = 12.sp
                    )
                },
                selected = selectedTabIndex == index,
                onClick = { onTabSelected(index) },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = MaterialTheme.colorScheme.primary,
                    selectedTextColor = MaterialTheme.colorScheme.primary,
                    unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    indicatorColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    }
}
