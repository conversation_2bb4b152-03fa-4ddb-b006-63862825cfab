package io.gigsta.domain.utils

import kotlinx.serialization.json.*
import kotlinx.serialization.KSerializer
import kotlinx.serialization.serializer

/**
 * Utility functions for JSON parsing and serialization
 */
object JsonUtils {
    
    /**
     * Lenient JSON configuration for parsing API responses
     * - Ignores unknown keys that don't exist in data classes
     * - Allows more flexible JSON parsing
     * - Converts incompatible values when possible
     */
    private val lenientJson = Json {
        ignoreUnknownKeys = true
        isLenient = true
        coerceInputValues = true
        encodeDefaults = true
    }
    
    /**
     * Parse JSON element to a specific data type with lenient configuration
     * 
     * @param T The target data type
     * @param jsonElement The JSON element to parse
     * @param serializer The serializer for type T
     * @return Parsed object of type T, or null if parsing fails
     */
    inline fun <reified T> parseJsonElement(
        jsonElement: JsonElement?,
        serializer: KSerializer<T> = serializer()
    ): T? {
        return try {
            jsonElement?.let { element ->
                println("Parsing JSON element to ${T::class.simpleName}: $element")
                val result = lenientJson.decodeFromJsonElement(serializer, element)
                println("Successfully parsed to ${T::class.simpleName}: $result")
                result
            }
        } catch (e: Exception) {
            println("Failed to parse JSON element to ${T::class.simpleName}: ${e.message}")
            println("JSON element: $jsonElement")
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Parse JSON string to a specific data type with lenient configuration
     * 
     * @param T The target data type
     * @param jsonString The JSON string to parse
     * @param serializer The serializer for type T
     * @return Parsed object of type T, or null if parsing fails
     */
    inline fun <reified T> parseJsonString(
        jsonString: String?,
        serializer: KSerializer<T> = serializer()
    ): T? {
        return try {
            jsonString?.let { json ->
                println("Parsing JSON string to ${T::class.simpleName}: $json")
                val result = lenientJson.decodeFromString(serializer, json)
                println("Successfully parsed to ${T::class.simpleName}: $result")
                result
            }
        } catch (e: Exception) {
            println("Failed to parse JSON string to ${T::class.simpleName}: ${e.message}")
            println("JSON string: $jsonString")
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Convert object to JSON element
     * 
     * @param T The source data type
     * @param obj The object to convert
     * @param serializer The serializer for type T
     * @return JSON element, or null if conversion fails
     */
    inline fun <reified T> toJsonElement(
        obj: T,
        serializer: KSerializer<T> = serializer()
    ): JsonElement? {
        return try {
            lenientJson.encodeToJsonElement(serializer, obj)
        } catch (e: Exception) {
            println("Failed to convert ${T::class.simpleName} to JSON element: ${e.message}")
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Convert object to JSON string
     * 
     * @param T The source data type
     * @param obj The object to convert
     * @param serializer The serializer for type T
     * @return JSON string, or null if conversion fails
     */
    inline fun <reified T> toJsonString(
        obj: T,
        serializer: KSerializer<T> = serializer()
    ): String? {
        return try {
            lenientJson.encodeToString(serializer, obj)
        } catch (e: Exception) {
            println("Failed to convert ${T::class.simpleName} to JSON string: ${e.message}")
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Check if a JSON element is null or empty
     * 
     * @param jsonElement The JSON element to check
     * @return true if the element is null, JsonNull, or empty
     */
    fun isNullOrEmpty(jsonElement: JsonElement?): Boolean {
        return when (jsonElement) {
            null -> true
            is JsonNull -> true
            is JsonObject -> jsonElement.isEmpty()
            is JsonArray -> jsonElement.isEmpty()
            is JsonPrimitive -> jsonElement.isString && jsonElement.content.isBlank()
        }
    }
    
    /**
     * Safely get a string value from a JSON object
     * 
     * @param jsonObject The JSON object
     * @param key The key to look for
     * @return String value or null if not found or not a string
     */
    fun getStringOrNull(jsonObject: JsonObject, key: String): String? {
        return try {
            jsonObject[key]?.jsonPrimitive?.contentOrNull
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Safely get an integer value from a JSON object
     * 
     * @param jsonObject The JSON object
     * @param key The key to look for
     * @return Integer value or null if not found or not an integer
     */
    fun getIntOrNull(jsonObject: JsonObject, key: String): Int? {
        return try {
            jsonObject[key]?.jsonPrimitive?.intOrNull
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Safely get a boolean value from a JSON object
     * 
     * @param jsonObject The JSON object
     * @param key The key to look for
     * @return Boolean value or null if not found or not a boolean
     */
    fun getBooleanOrNull(jsonObject: JsonObject, key: String): Boolean? {
        return try {
            jsonObject[key]?.jsonPrimitive?.booleanOrNull
        } catch (e: Exception) {
            null
        }
    }
}
