package io.gigsta.data.repository

import io.gigsta.data.datasource.EmailDataSource
import io.gigsta.data.datasource.LetterDataSource
import io.gigsta.data.datasource.ResumeDataSource
import io.gigsta.domain.model.*
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.domain.repository.HistoryRepository
import io.gigsta.domain.utils.getLetterTemplateById
import io.gigsta.domain.utils.getResumeTemplateById
import io.gigsta.domain.utils.JsonUtils

class HistoryRepositoryImpl(
    private val authRepository: AuthRepository,
    private val resumeDataSource: ResumeDataSource = ResumeDataSource(),
    private val letterDataSource: LetterDataSource = LetterDataSource(),
    private val emailDataSource: EmailDataSource = EmailDataSource()
) : HistoryRepository {

    private suspend fun getCurrentUserId(): String? {
        return authRepository.getCurrentUser()?.id
    }


    
    override suspend fun getHistoryItems(type: HistoryItemType): List<BaseHistoryItem> {
        val userId = getCurrentUserId() ?: return emptyList()

        return when (type) {
            HistoryItemType.CV_BUILDER -> {
                resumeDataSource.getResumeHistory(userId)
                    .getOrElse {
                        println("Failed to fetch resume history: ${it.message}")
                        emptyList()
                    }
                    .map { resume ->
                        val parsedStructuredData = JsonUtils.parseJsonElement<StructuredResumeData>(resume.structuredData)

                        ResumeHistoryItem(
                            id = resume.id,
                            createdAt = resume.createdAt,
                            templateId = resume.templateId,
                            templateName = getResumeTemplateById(resume.templateId)?.name,
                            structuredData = parsedStructuredData,
                            htmlContent = resume.htmlContent,
                            tokensDeducted = resume.tokensDeducted
                        )
                    }
            }
            HistoryItemType.APPLICATION_LETTER -> {
                letterDataSource.getLetterHistory(userId)
                    .getOrElse {
                        println("Failed to fetch letter history: ${it.message}")
                        emptyList()
                    }
                    .map { letter ->
                        LetterHistoryItem(
                            id = letter.id,
                            createdAt = letter.createdAt,
                            templateId = letter.templateId,
                            templateName = getLetterTemplateById(letter.templateId)?.name,
                            plainText = letter.plainText,
                            designHtml = letter.designHtml,
                        )
                    }
            }
            HistoryItemType.EMAIL_APPLICATION -> {
                emailDataSource.getEmailHistory(userId)
                    .getOrElse {
                        println("Failed to fetch email history: ${it.message}")
                        emptyList()
                    }
                    .map { email ->
                        EmailHistoryItem(
                            id = email.id,
                            createdAt = email.createdAt,
                            subject = email.subject,
                            body = email.body
                        )
                    }
            }
        }
    }
    
    override suspend fun createHistoryItem(item: BaseHistoryItem): Result<BaseHistoryItem> {
        // TODO: Implement actual creation logic
        return Result.success(item)
    }

    override suspend fun updateHistoryItem(item: BaseHistoryItem): Result<BaseHistoryItem> {
        // TODO: Implement actual update logic
        return Result.success(item)
    }

    override suspend fun deleteHistoryItem(id: String): Result<Unit> {
        // TODO: Implement actual deletion logic
        return Result.success(Unit)
    }
}
